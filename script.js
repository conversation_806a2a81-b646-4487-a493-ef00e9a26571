// PDF Page Deleter JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const uploadArea = document.querySelector('.upload-area');
            const fileInput = document.getElementById('fileInput');
            const fileInfo = document.querySelector('.file-info');
            const deleteOptions = document.querySelector('.delete-options');
            const processingStatus = document.querySelector('.processing-status');
            const downloadSection = document.querySelector('.download-section');
            const messageContainer = document.querySelector('.message-container');
            
            // File info elements
            const fileName = document.querySelector('.file-name span');
            const pageCount = document.querySelector('.page-count span');
            
            // Option elements
            const optionCards = document.querySelectorAll('.option-card');
            const radioButtons = document.querySelectorAll('input[name="deleteOption"]');
            const pageInputs = document.querySelectorAll('.page-input');
            
            // Button elements
            const processBtn = document.querySelector('.action-buttons .btn-primary');
            const resetBtn = document.querySelector('.action-buttons .btn-secondary');
            const downloadLink = document.querySelector('.download-actions .btn-success');
            const processAnotherBtn = document.querySelector('.additional-actions .btn-secondary');
            
            // Progress elements
            const progressFill = document.querySelector('.progress-fill');
            const originalPagesSpan = document.querySelector('.original-pages');
            const newPagesSpan = document.querySelector('.new-pages');
            
            // Variables
            let currentPDF = null;
            let pdfDoc = null;
            let totalPages = 0;
            let processedPDFBlob = null;
            
            // Initialize the application
            init();
            
            function init() {
                hideAllSections();
                setupEventListeners();
                updateOptionCards();
            }
            
            function hideAllSections() {
                fileInfo.style.display = 'none';
                deleteOptions.style.display = 'none';
                processingStatus.style.display = 'none';
                downloadSection.style.display = 'none';
                hideAllMessages();
            }
            
            function setupEventListeners() {
                // File input events
                fileInput.addEventListener('change', handleFileSelect);
                
                // Upload area events
                uploadArea.addEventListener('click', (e) => {
                    if (e.target === uploadArea || e.target.closest('.upload-icon') || e.target.closest('.upload-text') || e.target.closest('.upload-subtitle')) {
                        fileInput.click();
                    }
                });
                
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);
                
                // Option card events
                optionCards.forEach((card, index) => {
                    card.addEventListener('click', () => selectOption(index));
                });
                
                radioButtons.forEach((radio, index) => {
                    radio.addEventListener('change', () => selectOption(index));
                });
                
                // Button events
                processBtn.addEventListener('click', processPDF);
                resetBtn.addEventListener('click', resetApplication);
                processAnotherBtn.addEventListener('click', resetApplication);
                
                // Message close events
                document.querySelectorAll('.message-close').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.target.closest('.message').style.display = 'none';
                    });
                });
                
                // Page input validation
                pageInputs.forEach(input => {
                    input.addEventListener('input', validatePageInput);
                });
            }
            
            function handleDragOver(e) {
                e.preventDefault();
                uploadArea.classList.add('dragging');
            }
            
            function handleDragLeave(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragging');
            }
            
            function handleDrop(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragging');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            }
            
            function handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFile(file);
                }
            }
            
            async function handleFile(file) {
                // Validate file type
                if (file.type !== 'application/pdf') {
                    showMessage('error', 'Please select a valid PDF file.');
                    return;
                }
                
                // Validate file size (10MB limit)
                if (file.size > 10 * 1024 * 1024) {
                    showMessage('warning', 'File size limit: 10MB maximum. Please choose a smaller file.');
                    return;
                }
                
                try {
                    // Read the PDF file
                    const arrayBuffer = await file.arrayBuffer();
                    pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                    totalPages = pdfDoc.getPageCount();
                    currentPDF = file;
                    
                    // Update UI
                    fileName.textContent = file.name;
                    pageCount.textContent = `Total pages: ${totalPages}`;
                    
                    // Show file info and options
                    fileInfo.style.display = 'block';
                    deleteOptions.style.display = 'block';
                    
                    // Hide upload area
                    uploadArea.style.display = 'none';
                    
                    hideAllMessages();
                    showMessage('success', `PDF loaded successfully! ${totalPages} pages found.`);
                    
                } catch (error) {
                    console.error('Error loading PDF:', error);
                    showMessage('error', 'Error loading PDF file. Please try again.');
                }
            }
            
            function selectOption(index) {
                // Update radio button
                radioButtons[index].checked = true;
                
                // Update visual state
                updateOptionCards();
            }
            
            function updateOptionCards() {
                optionCards.forEach((card, index) => {
                    if (radioButtons[index].checked) {
                        card.classList.add('active');
                    } else {
                        card.classList.remove('active');
                    }
                });
            }
            
            function validatePageInput(e) {
                const input = e.target;
                const value = input.value.trim();
                
                if (!value) return;
                
                const selectedOption = document.querySelector('input[name="deleteOption"]:checked').value;
                let isValid = false;
                
                if (selectedOption === 'specific' || selectedOption === 'keep') {
                    // Validate comma-separated page numbers
                    const pages = value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
                    isValid = pages.length > 0 && pages.every(p => p >= 1 && p <= totalPages);
                } else if (selectedOption === 'range') {
                    // Validate page range
                    const rangeParts = value.split('-');
                    if (rangeParts.length === 2) {
                        const start = parseInt(rangeParts[0].trim());
                        const end = parseInt(rangeParts[1].trim());
                        isValid = !isNaN(start) && !isNaN(end) && start >= 1 && end <= totalPages && start <= end;
                    }
                }
                
                // Visual feedback
                if (isValid || !value) {
                    input.style.borderColor = '';
                    input.style.backgroundColor = '';
                } else {
                    input.style.borderColor = 'var(--error-color)';
                    input.style.backgroundColor = 'var(--error-light)';
                }
            }
            
            async function processPDF() {
                if (!pdfDoc) {
                    showMessage('error', 'Please select a PDF file first.');
                    return;
                }
                
                const selectedOption = document.querySelector('input[name="deleteOption"]:checked').value;
                const activeInput = document.querySelector('.option-card.active .page-input');
                const inputValue = activeInput.value.trim();
                
                if (!inputValue) {
                    showMessage('error', 'Please enter page numbers or range.');
                    return;
                }
                
                let pagesToDelete = [];
                
                try {
                    // Parse input based on selected option
                    if (selectedOption === 'specific') {
                        const pages = inputValue.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
                        if (pages.some(p => p < 1 || p > totalPages)) {
                            throw new Error('Invalid page numbers');
                        }
                        pagesToDelete = pages.map(p => p - 1); // Convert to 0-based index
                    } else if (selectedOption === 'range') {
                        const rangeParts = inputValue.split('-');
                        if (rangeParts.length !== 2) {
                            throw new Error('Invalid range format');
                        }
                        const start = parseInt(rangeParts[0].trim());
                        const end = parseInt(rangeParts[1].trim());
                        if (isNaN(start) || isNaN(end) || start < 1 || end > totalPages || start > end) {
                            throw new Error('Invalid page range');
                        }
                        for (let i = start - 1; i < end; i++) {
                            pagesToDelete.push(i);
                        }
                    } else if (selectedOption === 'keep') {
                        const pagesToKeep = inputValue.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
                        if (pagesToKeep.some(p => p < 1 || p > totalPages)) {
                            throw new Error('Invalid page numbers');
                        }
                        const keepIndices = pagesToKeep.map(p => p - 1);
                        for (let i = 0; i < totalPages; i++) {
                            if (!keepIndices.includes(i)) {
                                pagesToDelete.push(i);
                            }
                        }
                    }
                    
                    if (pagesToDelete.length === 0) {
                        showMessage('warning', 'No pages selected for deletion.');
                        return;
                    }
                    
                    if (pagesToDelete.length >= totalPages) {
                        showMessage('error', 'Cannot delete all pages. At least one page must remain.');
                        return;
                    }
                    
                    // Show processing status
                    deleteOptions.style.display = 'none';
                    processingStatus.style.display = 'block';
                    
                    // Simulate processing with progress
                    await simulateProgress();
                    
                    // Create new PDF without deleted pages
                    const newPDF = await PDFLib.PDFDocument.create();
                    const pages = await newPDF.copyPages(pdfDoc, Array.from({length: totalPages}, (_, i) => i));
                    
                    // Add pages that are not in the delete list
                    for (let i = 0; i < totalPages; i++) {
                        if (!pagesToDelete.includes(i)) {
                            newPDF.addPage(pages[i]);
                        }
                    }
                    
                    // Generate PDF blob
                    const pdfBytes = await newPDF.save();
                    processedPDFBlob = new Blob([pdfBytes], { type: 'application/pdf' });
                    
                    // Update UI
                    const remainingPages = totalPages - pagesToDelete.length;
                    originalPagesSpan.textContent = `Original: ${totalPages} pages`;
                    newPagesSpan.textContent = `Modified: ${remainingPages} pages`;
                    
                    // Create download link
                    const downloadUrl = URL.createObjectURL(processedPDFBlob);
                    downloadLink.href = downloadUrl;
                    downloadLink.download = `modified_${currentPDF.name}`;
                    
                    // Show download section
                    processingStatus.style.display = 'none';
                    downloadSection.style.display = 'block';
                    
                    showMessage('success', `PDF processed successfully! ${pagesToDelete.length} pages removed.`);
                    
                } catch (error) {
                    console.error('Error processing PDF:', error);
                    processingStatus.style.display = 'none';
                    deleteOptions.style.display = 'block';
                    showMessage('error', 'Error processing PDF. Please check your input and try again.');
                }
            }
            
            async function simulateProgress() {
                return new Promise(resolve => {
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += Math.random() * 15;
                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);
                            setTimeout(resolve, 500);
                        }
                        progressFill.style.width = `${progress}%`;
                    }, 200);
                });
            }
            
            function resetApplication() {
                // Reset variables
                currentPDF = null;
                pdfDoc = null;
                totalPages = 0;
                processedPDFBlob = null;
                
                // Reset form
                fileInput.value = '';
                pageInputs.forEach(input => {
                    input.value = '';
                    input.style.borderColor = '';
                    input.style.backgroundColor = '';
                });
                
                // Reset radio buttons
                radioButtons[0].checked = true;
                updateOptionCards();
                
                // Reset progress
                progressFill.style.width = '0%';
                
                // Reset UI
                uploadArea.style.display = 'block';
                hideAllSections();
                hideAllMessages();
                
                // Revoke download URL if exists
                if (downloadLink.href && downloadLink.href.startsWith('blob:')) {
                    URL.revokeObjectURL(downloadLink.href);
                    downloadLink.href = '#';
                }
            }
            
            function showMessage(type, text) {
                hideAllMessages();
                
                const message = document.querySelector(`.message.${type}`);
                if (message) {
                    const textSpan = message.querySelector('span');
                    if (textSpan) {
                        textSpan.textContent = text;
                    }
                    message.style.display = 'flex';
                    
                    // Auto-hide success messages after 5 seconds
                    if (type === 'success') {
                        setTimeout(() => {
                            message.style.display = 'none';
                        }, 5000);
                    }
                }
            }
            
            function hideAllMessages() {
                document.querySelectorAll('.message').forEach(msg => {
                    msg.style.display = 'none';
                });
            }
            
            // Handle page visibility changes to clean up resources
            document.addEventListener('visibilitychange', function() {
                if (document.hidden && downloadLink.href && downloadLink.href.startsWith('blob:')) {
                    // Clean up blob URLs when page is hidden
                    setTimeout(() => {
                        if (document.hidden) {
                            URL.revokeObjectURL(downloadLink.href);
                        }
                    }, 60000); // Clean up after 1 minute
                }
            });
            
            // Handle beforeunload to clean up resources
            window.addEventListener('beforeunload', function() {
                if (downloadLink.href && downloadLink.href.startsWith('blob:')) {
                    URL.revokeObjectURL(downloadLink.href);
                }
            });
        });

        // Error handling for PDF-lib loading
        window.addEventListener('error', function(e) {
            if (e.message.includes('PDFLib')) {
                console.error('PDF-lib failed to load. Please check your internet connection.');
                document.querySelector('.message.error span').textContent = 'Failed to load PDF processing library. Please refresh the page.';
                document.querySelector('.message.error').style.display = 'flex';
            }
        });