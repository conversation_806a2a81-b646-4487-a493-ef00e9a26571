/* CSS Variables for consistent theming */
        :root {
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #dbeafe;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --success-color: #10b981;
            --success-light: #d1fae5;
            --error-color: #ef4444;
            --error-light: #fee2e2;
            --warning-color: #f59e0b;
            --warning-light: #fef3c7;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --border-color: #e5e7eb;
            --border-hover: #d1d5db;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;
        }

        /* Reset and base styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        /* Container */
        .container {
            width: 100%;
            max-width: 1190px;
         min-height: 850px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--warning-color));
        }

        /* Header */
        h1 {
            text-align: center;
            padding: 2rem;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            background: var(--bg-secondary);
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        h1 i {
            color: var(--error-color);
            font-size: 2.25rem;
        }

        /* Upload Area */
        .upload-area {
            margin: 2rem;
            padding: 3rem 2rem;
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            text-align: center;
            background: var(--bg-secondary);
            transition: all var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area.dragging {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .upload-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            transition: transform var(--transition-normal);
        }

        .upload-area:hover .upload-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .upload-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .upload-subtitle {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .file-input {
            display: none;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--secondary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-primary);
        }

        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: var(--primary-light);
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.125rem;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        /* File Info */
        .file-info {
            margin: 2rem;
            display: none;
        }

        .info-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid var(--border-color);
            transition: all var(--transition-normal);
        }

        .info-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .file-name i {
            color: var(--error-color);
            font-size: 1.25rem;
        }

        .page-count {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .file-actions {
            margin-left: 1rem;
        }

        /* Delete Options */
        .delete-options {
            padding: 2rem;
            display: none;
        }

        .delete-options h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .delete-options h3 i {
            color: var(--warning-color);
        }

        .options-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .option-card {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            background: var(--bg-primary);
            transition: all var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform var(--transition-normal);
        }

        .option-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .option-card:hover::before {
            transform: scaleX(1);
        }

        .option-card.active {
            border-color: var(--primary-color);
            background: var(--primary-light);
            box-shadow: var(--shadow-md);
        }

        .option-card.active::before {
            transform: scaleX(1);
        }

        .option-card input[type="radio"] {
            display: none;
        }

        .option-label {
            display: block;
            cursor: pointer;
        }

        .option-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .option-header i {
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .option-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .option-content {
            margin-left: 2rem;
        }

        .page-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all var(--transition-fast);
            background: var(--bg-primary);
        }

        .page-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-hint {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .input-hint i {
            color: var(--primary-color);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        /* Processing Status */
        .processing-status {
            padding: 2rem;
            display: none;
        }

        .status-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .status-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .status-text h4 {
            font-size: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .status-text p {
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            width: 0%;
            border-radius: var(--radius-sm);
            transition: width var(--transition-normal);
        }

        /* Download Section */
        .download-section {
            padding: 2rem;
            display: none;
        }

        .download-card {
            background: var(--success-light);
            border: 2px solid var(--success-color);
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .download-icon {
            font-size: 3rem;
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        .download-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .download-details {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            font-size: 1rem;
            color: var(--text-secondary);
        }

        .download-details i {
            color: var(--success-color);
        }

        .original-pages,
        .new-pages {
            font-weight: 600;
        }

        .download-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .additional-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Messages */
        .message-container {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-width: 400px;
        }

        .message {
            display: none;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: var(--radius-md);
            font-weight: 500;
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            animation: slideIn 0.3s ease-out forwards;
            position: relative;
        }

        @keyframes slideIn {
            to { transform: translateX(0); }
        }

        .message.success {
            background: var(--success-light);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }

        .message.error {
            background: var(--error-light);
            color: var(--error-color);
            border-left: 4px solid var(--error-color);
        }

        .message.warning {
            background: var(--warning-light);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }

        .message-close {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            padding: 0.25rem;
            margin-left: auto;
            border-radius: var(--radius-sm);
            transition: background var(--transition-fast);
        }

        .message-close:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        /* Footer */
        .footer {
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
            padding: 2rem;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .footer-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .footer-info i {
            color: var(--success-color);
        }

        .footer-links {
            display: flex;
            gap: 2rem;
        }

        .footer-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            transition: color var(--transition-fast);
        }

        .footer-link:hover {
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 1rem 0.5rem;
            }
            
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            h1 {
                font-size: 2rem;
                padding: 1.5rem;
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .upload-area {
                margin: 1rem;
                padding: 2rem 1rem;
            }
            
            .upload-icon {
                font-size: 3rem;
            }
            
            .upload-text {
                font-size: 1.25rem;
            }
            
            .file-info,
            .delete-options,
            .processing-status,
            .download-section {
                margin: 1rem;
                padding: 1rem;
            }
            
            .info-card {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .file-actions {
                margin-left: 0;
            }
            
            .action-buttons,
            .download-actions,
            .additional-actions {
                flex-direction: column;
            }
            
            .btn-large {
                width: 100%;
                justify-content: center;
            }
            
            .footer-content {
                flex-direction: column;
                text-align: center;
            }
            
            .footer-links {
                justify-content: center;
            }
            
            .message-container {
                left: 1rem;
                right: 1rem;
                max-width: none;
            }
            
            .download-details {
                flex-direction: column;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .upload-area {
                padding: 1.5rem 0.5rem;
            }
            
            .option-content {
                margin-left: 0;
            }
            
            .option-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Focus styles for accessibility */
        .btn:focus,
        .page-input:focus,
        input[type="radio"]:focus + .option-label {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #0066cc;
                --text-primary: #000000;
                --text-secondary: #333333;
                --border-color: #666666;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }