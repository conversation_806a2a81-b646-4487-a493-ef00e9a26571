<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Local CSS -->
<link rel="stylesheet" href="style.css">
<!-- PDF-lib for PDF manipulation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>

<script src="script.js"></script>

<div class="container">
        <h1><i class="fas fa-file-pdf"></i> PDF Page Deleter</h1>
        
        <!-- Upload Section -->
        <div class="upload-area">
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">Upload a PDF file</div>
            <p class="upload-subtitle">Drag & drop your file here or click to browse</p>
            <input type="file" id="fileInput" class="file-input" accept=".pdf">
            <label for="fileInput" class="btn btn-primary">
                <i class="fas fa-folder-open"></i> Select PDF
            </label>
        </div>
        
        <!-- File Information Display -->
        <div class="file-info">
            <div class="info-card">
                <div class="file-details">
                    <div class="file-name">
                        <i class="fas fa-file-pdf"></i>
                        <span>sample-document.pdf</span>
                    </div>
                    <div class="page-count">
                        <i class="fas fa-copy"></i>
                        <span>Total pages: 10</span>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-outline btn-sm">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Delete Options Section -->
        <div class="delete-options">
            <h3><i class="fas fa-scissors"></i> Choose Deletion Method</h3>
            
            <div class="options-grid">
                <!-- Specific Pages Option -->
                <div class="option-card active">
                    <input type="radio" name="deleteOption" id="specificPages" value="specific" checked>
                    <label for="specificPages" class="option-label">
                        <div class="option-header">
                            <i class="fas fa-trash-alt"></i>
                            <span class="option-title">Delete Specific Pages</span>
                        </div>
                        <div class="option-content">
                            <input type="text" class="page-input" placeholder="e.g., 1,3,5,7">
                            <div class="input-hint">
                                <i class="fas fa-info-circle"></i>
                                Enter page numbers separated by commas
                            </div>
                        </div>
                    </label>
                </div>
                
                <!-- Page Range Option -->
                <div class="option-card">
                    <input type="radio" name="deleteOption" id="rangePages" value="range">
                    <label for="rangePages" class="option-label">
                        <div class="option-header">
                            <i class="fas fa-eraser"></i>
                            <span class="option-title">Delete Page Range</span>
                        </div>
                        <div class="option-content">
                            <input type="text" class="page-input" placeholder="e.g., 2-5">
                            <div class="input-hint">
                                <i class="fas fa-info-circle"></i>
                                Enter a page range (start-end)
                            </div>
                        </div>
                    </label>
                </div>
                
                <!-- Keep Only Option -->
                <div class="option-card">
                    <input type="radio" name="deleteOption" id="keepOnly" value="keep">
                    <label for="keepOnly" class="option-label">
                        <div class="option-header">
                            <i class="fas fa-bookmark"></i>
                            <span class="option-title">Keep Only These Pages</span>
                        </div>
                        <div class="option-content">
                            <input type="text" class="page-input" placeholder="e.g., 2,4,6,8">
                            <div class="input-hint">
                                <i class="fas fa-info-circle"></i>
                                Enter pages to keep, delete all others
                            </div>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary btn-large">
                    <i class="fas fa-scissors"></i>
                    Process PDF
                </button>
                <button class="btn btn-secondary btn-large">
                    <i class="fas fa-undo"></i>
                    Reset
                </button>
            </div>
        </div>
        
        <!-- Processing Status -->
        <div class="processing-status">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <div class="status-text">
                    <h4>Processing your PDF...</h4>
                    <p>Please wait while we remove the selected pages</p>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        </div>
        
        <!-- Download Section -->
        <div class="download-section">
            <div class="download-card">
                <div class="download-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="download-content">
                    <h3>Your PDF is ready!</h3>
                    <p class="download-details">
                        <span class="original-pages">Original: 10 pages</span>
                        <i class="fas fa-arrow-right"></i>
                        <span class="new-pages">Modified: 7 pages</span>
                    </p>
                    <div class="download-actions">
                        <a href="#" class="btn btn-success btn-large" download>
                            <i class="fas fa-download"></i>
                            Download Modified PDF
                        </a>
                        <button class="btn btn-outline btn-large">
                            <i class="fas fa-eye"></i>
                            Preview Changes
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Additional Actions -->
            <div class="additional-actions">
                <button class="btn btn-secondary">
                    <i class="fas fa-plus"></i>
                    Process Another PDF
                </button>
                <button class="btn btn-outline">
                    <i class="fas fa-share-alt"></i>
                    Share
                </button>
            </div>
        </div>
        
        <!-- Messages -->
        <div class="message-container">
            <div class="message success">
                <i class="fas fa-check-circle"></i>
                <span>PDF processed successfully! 3 pages removed.</span>
                <button class="message-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="message error">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Error: Please select valid page numbers within the document range.</span>
                <button class="message-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="message warning">
                <i class="fas fa-info-circle"></i>
                <span>File size limit: 10MB maximum. Please choose a smaller file.</span>
                <button class="message-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <!-- Footer -->
        <footer class="footer">
            <!-- Footer content removed as requested -->
        </footer>
    </div>