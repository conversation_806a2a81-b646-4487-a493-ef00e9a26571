<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Page Remover</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PDF-lib for PDF manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .main-page {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }

        .main-title {
            font-size: 3rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .main-description {
            font-size: 1.25rem;
            color: #6b7280;
            margin-bottom: 3rem;
        }

        .select-file-btn {
            width: 320px;
            height: 90px;
            font-size: 1.25rem;
            font-weight: 600;
            background-color: #3b82f6;
            border: none;
            border-radius: 8px;
            color: white;
            transition: all 0.3s ease;
        }

        .select-file-btn:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .file-input {
            display: none;
        }

        .page-removal-interface {
            display: none;
            background-color: #ffffff;
            min-height: 100vh;
            padding: 2rem 0;
        }

        .main-container {
            max-width: 1190px;
            min-height: 900px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }

        .header-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .file-name-display {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .page-count-display {
            font-size: 1rem;
            color: #6b7280;
        }

        .page-preview-section {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .page-cards-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .page-card {
            width: 180px;
            height: 265px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f9fafb;
            position: relative;
        }

        .page-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .page-card.selected {
            border-color: #3b82f6;
            background-color: #dbeafe;
        }

        .page-checkbox {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
        }

        .page-thumbnail {
            width: 100%;
            height: 180px;
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 3rem;
            color: #9ca3af;
        }

        .page-number {
            font-weight: 600;
            color: #374151;
        }
    </style>
</head>
<body>
    <!-- Main Page (Before File Upload) -->
    <div id="mainPage" class="main-page">
        <h1 class="main-title">PDF Page Remover</h1>
        <p class="main-description">Easily remove pages from PDF files</p>
        <button class="btn select-file-btn" onclick="document.getElementById('fileInput').click()">
            Select PDF File
        </button>
        <input type="file" id="fileInput" class="file-input" accept=".pdf" onchange="handleFileUpload(event)">
    </div>
    <!-- Page Removal Interface (After File Upload) -->
    <div id="pageRemovalInterface" class="page-removal-interface">
        <div class="container">
            <div class="main-container">
                <!-- Header Section -->
                <div class="header-section">
                    <div class="file-name-display" id="fileNameDisplay">sample-document.pdf</div>
                    <div class="page-count-display" id="pageCountDisplay">Total pages: 10</div>
                </div>

                <!-- Page Preview Section -->
                <div class="page-preview-section">
                    <div class="page-cards-container" id="pageCardsContainer">
                        <!-- Page cards will be dynamically generated here -->
                    </div>
                </div>

                <!-- Control Fields -->
                <div class="control-fields">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="deletePageRange" class="form-label">Delete Page Range</label>
                            <input type="text" class="form-control" id="deletePageRange" placeholder="e.g., 1,3,5 or 2-5" readonly>
                            <div class="form-text">Automatically populated with selected page numbers</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="keepOnlyPages" class="form-label">Keep Only These Pages</label>
                            <input type="text" class="form-control" id="keepOnlyPages" placeholder="e.g., 2,4,6,8" readonly>
                            <div class="form-text">Option to delete all pages except selected ones</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="reverseOrder">
                                <label class="form-check-label" for="reverseOrder">
                                    Reverse Page Order
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="deleteBlankPages">
                                <label class="form-check-label" for="deleteBlankPages">
                                    Delete Blank Pages
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Section -->
                <div class="footer-section text-center">
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <button class="btn btn-danger btn-lg" id="deleteSelectedBtn" onclick="deleteSelectedPages()">
                            Delete Selected Pages
                        </button>
                        <button class="btn btn-success btn-lg" id="keepOnlyBtn" onclick="keepOnlySelected()">
                            Keep Only Selected
                        </button>
                        <button class="btn btn-secondary btn-lg" onclick="startOver()">
                            Start Over
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentPDF = null;
        let pdfDoc = null;
        let totalPages = 0;
        let selectedPages = new Set();

        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            if (file.type !== 'application/pdf') {
                alert('Please select a valid PDF file.');
                return;
            }

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size limit: 10MB maximum. Please choose a smaller file.');
                return;
            }

            try {
                // Read the PDF file
                const arrayBuffer = await file.arrayBuffer();
                pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                totalPages = pdfDoc.getPageCount();
                currentPDF = file;

                // Update UI
                document.getElementById('fileNameDisplay').textContent = file.name;
                document.getElementById('pageCountDisplay').textContent = `Total pages: ${totalPages}`;

                // Generate page cards
                generatePageCards();

                // Hide main page and show page removal interface
                document.getElementById('mainPage').style.display = 'none';
                document.getElementById('pageRemovalInterface').style.display = 'block';

            } catch (error) {
                console.error('Error loading PDF:', error);
                alert('Error loading PDF file. Please try again.');
            }
        }

        function generatePageCards() {
            const container = document.getElementById('pageCardsContainer');
            container.innerHTML = '';

            for (let i = 1; i <= totalPages; i++) {
                const pageCard = document.createElement('div');
                pageCard.className = 'page-card';
                pageCard.onclick = () => togglePageSelection(i);

                pageCard.innerHTML = `
                    <input type="checkbox" class="page-checkbox" id="page-${i}" onchange="togglePageSelection(${i})">
                    <div class="page-thumbnail">
                        📄
                    </div>
                    <div class="page-number">Page ${i}</div>
                `;

                container.appendChild(pageCard);
            }
        }

        function togglePageSelection(pageNumber) {
            const checkbox = document.getElementById(`page-${pageNumber}`);
            const pageCard = checkbox.closest('.page-card');

            if (selectedPages.has(pageNumber)) {
                selectedPages.delete(pageNumber);
                pageCard.classList.remove('selected');
                checkbox.checked = false;
            } else {
                selectedPages.add(pageNumber);
                pageCard.classList.add('selected');
                checkbox.checked = true;
            }

            updateControlFields();
        }

        function updateControlFields() {
            const selectedArray = Array.from(selectedPages).sort((a, b) => a - b);
            const deletePageRange = document.getElementById('deletePageRange');
            const keepOnlyPages = document.getElementById('keepOnlyPages');

            // Update delete page range field
            deletePageRange.value = selectedArray.join(', ');

            // Update keep only pages field (all pages except selected)
            const allPages = Array.from({length: totalPages}, (_, i) => i + 1);
            const keepPages = allPages.filter(page => !selectedPages.has(page));
            keepOnlyPages.value = keepPages.join(', ');
        }

        async function deleteSelectedPages() {
            if (selectedPages.size === 0) {
                alert('Please select pages to delete.');
                return;
            }

            if (selectedPages.size >= totalPages) {
                alert('Cannot delete all pages. At least one page must remain.');
                return;
            }

            try {
                // Create new PDF without selected pages
                const newPDF = await PDFLib.PDFDocument.create();
                const pages = await newPDF.copyPages(pdfDoc, Array.from({length: totalPages}, (_, i) => i));

                // Add pages that are not selected for deletion
                for (let i = 0; i < totalPages; i++) {
                    if (!selectedPages.has(i + 1)) {
                        newPDF.addPage(pages[i]);
                    }
                }

                // Generate and download PDF
                await downloadProcessedPDF(newPDF, `deleted_pages_${currentPDF.name}`);

            } catch (error) {
                console.error('Error processing PDF:', error);
                alert('Error processing PDF. Please try again.');
            }
        }

        async function keepOnlySelected() {
            if (selectedPages.size === 0) {
                alert('Please select pages to keep.');
                return;
            }

            try {
                // Create new PDF with only selected pages
                const newPDF = await PDFLib.PDFDocument.create();
                const selectedArray = Array.from(selectedPages).sort((a, b) => a - b);
                const pageIndices = selectedArray.map(p => p - 1); // Convert to 0-based index
                const pages = await newPDF.copyPages(pdfDoc, pageIndices);

                // Add selected pages in order
                pages.forEach(page => newPDF.addPage(page));

                // Generate and download PDF
                await downloadProcessedPDF(newPDF, `keep_only_${currentPDF.name}`);

            } catch (error) {
                console.error('Error processing PDF:', error);
                alert('Error processing PDF. Please try again.');
            }
        }

        async function downloadProcessedPDF(pdfDoc, filename) {
            // Generate PDF blob
            const pdfBytes = await pdfDoc.save();
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });

            // Create download link
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('PDF processed successfully!');
        }

        function startOver() {
            // Reset variables
            currentPDF = null;
            pdfDoc = null;
            totalPages = 0;
            selectedPages.clear();

            // Reset file input
            document.getElementById('fileInput').value = '';

            // Show main page and hide page removal interface
            document.getElementById('mainPage').style.display = 'flex';
            document.getElementById('pageRemovalInterface').style.display = 'none';
        }
    </script>
</body>
</html>